{"rustc": 15497389221046826682, "features": "[\"default\", \"rwh_06\"]", "declared_features": "[\"all\", \"api-level-23\", \"api-level-24\", \"api-level-25\", \"api-level-26\", \"api-level-27\", \"api-level-28\", \"api-level-29\", \"api-level-30\", \"api-level-31\", \"api-level-32\", \"api-level-33\", \"audio\", \"bitmap\", \"default\", \"jni\", \"media\", \"nativewindow\", \"rwh_04\", \"rwh_05\", \"rwh_06\", \"sync\", \"test\"]", "target": 4479777621832623082, "profile": 15657897354478470176, "path": 18353486713829275958, "deps": [[2395061796398438895, "ffi", false, 8256926320414139107], [4143744114649553716, "rwh_06", false, 5648965349049443791], [5986029879202738730, "log", false, 7193723165178592537], [7896293946984509699, "bitflags", false, 15585780327617686466], [8008191657135824715, "thiserror", false, 9297849075749335318], [15987728108628015046, "jni_sys", false, 4315269086314376569], [16712258961403650142, "num_enum", false, 10706797474583465592]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/ndk-483f0115baf0607e/dep-lib-ndk", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 14419220508014593114, "compile_kind": 6373778340919622063}