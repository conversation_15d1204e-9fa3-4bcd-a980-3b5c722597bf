{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"borsh\", \"c-repr\", \"db-diesel-mysql\", \"db-diesel-postgres\", \"db-diesel2-mysql\", \"db-diesel2-postgres\", \"db-postgres\", \"db-tokio-postgres\", \"default\", \"diesel\", \"legacy-ops\", \"macros\", \"maths\", \"maths-nopanic\", \"ndarray\", \"proptest\", \"rand\", \"rand-0_9\", \"rkyv\", \"rkyv-safe\", \"rocket-traits\", \"rust-fuzz\", \"serde\", \"serde-arbitrary-precision\", \"serde-bincode\", \"serde-float\", \"serde-str\", \"serde-with-arbitrary-precision\", \"serde-with-float\", \"serde-with-str\", \"serde_json\", \"std\", \"tokio-pg\", \"tokio-postgres\"]", "target": 10284609753004012519, "profile": 15657897354478470176, "path": 1648859822735756516, "deps": [[5157631553186200874, "num_traits", false, 7868377299595363119], [13847662864258534762, "arrayvec", false, 888984759524200444], [16119793329258425851, "build_script_build", false, 6830884010668535041]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/rust_decimal-9bd4f70c2e6d5e39/dep-lib-rust_decimal", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 14419220508014593114, "compile_kind": 6373778340919622063}