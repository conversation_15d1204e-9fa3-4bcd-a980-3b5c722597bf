# Tauri 2.0 + React + TypeScript + Android 开发环境搭建指南

## 📋 概述

本指南将帮你搭建一个完整的 Tauri 2.0 开发环境，使用 React + TypeScript 开发跨平台应用，特别是 Android 应用。

## 🛠️ 环境要求

### 必须安装的工具

1. **Node.js** (v18+)
2. **Rust** (最新稳定版)
3. **Java JDK** (OpenJDK 17)
4. **Android Studio** (包含 Android SDK)
5. **Android NDK**

## 📦 第一步：安装基础环境

### 1.1 安装 Node.js

```bash
# 使用 Homebrew (macOS)
brew install node

# 验证安装
node --version
npm --version
```

### 1.2 安装 Rust

```bash
# 安装 Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 重新加载环境变量
source ~/.cargo/env

# 验证安装
rustc --version
cargo --version
```

### 1.3 安装 Java JDK

```bash
# 使用 Homebrew 安装 OpenJDK 17
brew install openjdk@17

# 验证安装
java -version
```

### 1.4 安装 Android Studio

1. 下载并安装 [Android Studio](https://developer.android.com/studio)
2. 启动 Android Studio，完成初始设置
3. 安装 Android SDK（通常会自动安装）

## 🔧 第二步：配置 Android 开发环境

### 2.1 设置环境变量

```bash
# 编辑 ~/.zshrc 或 ~/.bash_profile
export ANDROID_HOME=~/Library/Android/sdk
export NDK_HOME=$ANDROID_HOME/ndk/26.3.11579264
export JAVA_HOME=/opt/homebrew/opt/openjdk@17
export PATH=$JAVA_HOME/bin:$PATH
export PATH=$ANDROID_HOME/platform-tools:$PATH

# 重新加载配置
source ~/.zshrc  # 或 source ~/.bash_profile
```

### 2.2 安装 Android NDK

```bash
# 方法1：通过 Android Studio
# SDK Manager -> SDK Tools -> NDK (Side by side)

# 方法2：手动下载安装
cd ~/Library/Android/sdk
curl -O https://dl.google.com/android/repository/android-ndk-r26d-darwin.zip
unzip -q android-ndk-r26d-darwin.zip
mv android-ndk-r26d ndk/26.3.11579264
rm android-ndk-r26d-darwin.zip
```

## 🚀 第三步：安装 Tauri CLI

```bash
# 安装 Tauri 2.0 CLI
npm install -g @tauri-apps/cli@next

# 验证安装
tauri --version
```

## 📱 第四步：创建项目

### 4.1 创建 React + TypeScript 项目

```bash
# 使用 Vite 创建 React + TypeScript 项目
npm create vite@latest my-tauri-app -- --template react-ts

# 进入项目目录
cd my-tauri-app

# 安装依赖
npm install
```

### 4.2 初始化 Tauri

```bash
# 初始化 Tauri 项目
tauri init

# 配置选项：
# - Web assets location: ../dist
# - Development server URL: http://localhost:5173
# - Frontend dev command: npm run dev
# - Frontend build command: npm run build
```

### 4.3 修改 Tauri 配置

编辑 `src-tauri/tauri.conf.json`：

```json
{
  "productName": "my-tauri-app",
  "identifier": "com.example.mytauriapp",
  "build": {
    "frontendDist": "../dist",
    "devUrl": "http://localhost:5173",
    "beforeDevCommand": "npm run dev",
    "beforeBuildCommand": "npm run build"
  }
}
```

### 4.4 初始化 Android 支持

```bash
# 设置环境变量并初始化 Android
export ANDROID_HOME=~/Library/Android/sdk
export NDK_HOME=$ANDROID_HOME/ndk/26.3.11579264
export JAVA_HOME=/opt/homebrew/opt/openjdk@17
export PATH=$JAVA_HOME/bin:$PATH

tauri android init
```

## 🏃‍♂️ 第五步：运行和开发

### 5.1 开发模式

```bash
# 在浏览器中开发（快速调试）
npm run dev

# 在 Android 设备/模拟器中开发
tauri android dev --open
```

### 5.2 构建 APK

```bash
# 构建 Android APK
export ANDROID_HOME=~/Library/Android/sdk
export NDK_HOME=$ANDROID_HOME/ndk/26.3.11579264
export JAVA_HOME=/opt/homebrew/opt/openjdk@17
export PATH=$JAVA_HOME/bin:$PATH

tauri android build
```

## 📁 项目结构说明

```
my-tauri-app/
├── index.html              # Vite 入口文件
├── package.json            # 前端依赖
├── vite.config.ts          # Vite 配置
├── tsconfig.json           # TypeScript 配置
├── public/                 # 静态资源
│   └── vite.svg
├── src/                    # React + TypeScript 源码
│   ├── App.tsx            # 主 React 组件
│   ├── main.tsx           # React 应用入口
│   ├── App.css            # 样式文件
│   └── assets/            # 前端资源
└── src-tauri/             # Tauri Rust 后端
    ├── src/
    │   ├── main.rs        # Rust 应用入口（桌面版）
    │   └── lib.rs         # Tauri 应用配置（移动版）
    ├── Cargo.toml         # Rust 依赖管理
    ├── tauri.conf.json    # Tauri 配置
    ├── build.rs           # 构建脚本
    ├── capabilities/      # 权限配置
    ├── icons/             # 应用图标
    └── gen/android/       # 生成的 Android 项目
```

## 🎯 开发工作流

### 前端开发（React + TypeScript）

1. 在 `src/` 目录下编写 React 组件
2. 使用 `npm run dev` 在浏览器中快速开发
3. 支持热重载，修改代码自动更新

### 后端开发（Rust）

1. 在 `src-tauri/src/` 目录下编写 Rust 代码
2. 使用 `tauri android dev` 测试原生功能
3. 可以调用系统 API、文件系统等

### Android 测试

1. 连接 Android 设备或启动模拟器
2. 运行 `tauri android dev --open`
3. 应用会自动安装到设备上

## ⚠️ 常见问题

### 编译时间长

- 首次编译需要 3-5 分钟（下载和编译 500+ 依赖包）
- 后续编译只需几秒到几十秒
- 不要删除 `target/` 目录（缓存编译结果）

### 环境变量问题

- 确保每次开发前都设置了环境变量
- 可以将环境变量写入 `~/.zshrc` 或 `~/.bash_profile`

### NDK 版本问题

- 使用 NDK 26.3.11579264 版本
- 确保 `NDK_HOME` 路径正确

## 🔥 高级配置

### 添加 Tauri API 支持

```bash
# 安装 Tauri API
npm install @tauri-apps/api
```

在 React 组件中使用：

```typescript
import { invoke } from '@tauri-apps/api/core';
import { open } from '@tauri-apps/plugin-dialog';

// 调用 Rust 函数
const result = await invoke('my_rust_function', { param: 'value' });

// 打开文件对话框
const file = await open();
```

### 配置应用权限

编辑 `src-tauri/capabilities/default.json`：

```json
{
  "identifier": "default",
  "description": "Default permissions",
  "windows": ["main"],
  "permissions": ["core:default", "dialog:default", "fs:default"]
}
```

### 自定义应用图标

1. 准备不同尺寸的图标文件
2. 放置在 `src-tauri/icons/` 目录
3. 运行 `tauri icon path/to/icon.png` 自动生成

## 💡 最佳实践

### 1. 开发环境脚本

创建 `scripts/dev-android.sh`：

```bash
#!/bin/bash
export ANDROID_HOME=~/Library/Android/sdk
export NDK_HOME=$ANDROID_HOME/ndk/26.3.11579264
export JAVA_HOME=/opt/homebrew/opt/openjdk@17
export PATH=$JAVA_HOME/bin:$PATH

tauri android dev --open
```

### 2. 构建脚本

创建 `scripts/build-android.sh`：

```bash
#!/bin/bash
export ANDROID_HOME=~/Library/Android/sdk
export NDK_HOME=$ANDROID_HOME/ndk/26.3.11579264
export JAVA_HOME=/opt/homebrew/opt/openjdk@17
export PATH=$JAVA_HOME/bin:$PATH

npm run build
tauri android build
```

### 3. 代码组织

```
src/
├── components/          # React 组件
├── hooks/              # 自定义 Hooks
├── utils/              # 工具函数
├── types/              # TypeScript 类型定义
├── services/           # API 服务
└── tauri/              # Tauri 相关代码
    ├── commands.ts     # Rust 命令调用
    └── events.ts       # 事件处理
```

## 🚀 部署和发布

### 生成签名密钥

```bash
# 生成 Android 签名密钥
keytool -genkey -v -keystore my-release-key.keystore -alias my-key-alias -keyalg RSA -keysize 2048 -validity 10000
```

### 配置签名

编辑 `src-tauri/gen/android/app/build.gradle`：

```gradle
android {
    signingConfigs {
        release {
            keyAlias 'my-key-alias'
            keyPassword 'your-key-password'
            storeFile file('path/to/my-release-key.keystore')
            storePassword 'your-store-password'
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
        }
    }
}
```

## 🎉 完成！

现在你已经有了一个完整的 Tauri 2.0 + React + TypeScript + Android 开发环境！

### 快速开始命令

```bash
# 1. 创建项目
npm create vite@latest my-app -- --template react-ts
cd my-app && npm install

# 2. 初始化 Tauri
tauri init

# 3. 初始化 Android
tauri android init

# 4. 开发模式
tauri android dev --open

# 5. 构建 APK
tauri android build
```

可以开始用熟悉的 React 语法开发跨平台应用了！🚀
