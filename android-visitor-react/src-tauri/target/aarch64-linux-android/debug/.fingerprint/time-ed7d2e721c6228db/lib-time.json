{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"formatting\", \"local-offset\", \"macros\", \"parsing\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"formatting\", \"large-dates\", \"local-offset\", \"macros\", \"parsing\", \"quickcheck\", \"rand\", \"serde\", \"serde-human-readable\", \"serde-well-known\", \"std\", \"wasm-bindgen\"]", "target": 3713843603798095488, "profile": 17564239539340744749, "path": 2118810449863577464, "deps": [[253581978874359338, "deranged", false, 7900727014605002751], [724804171976944018, "num_conv", false, 2024514195949268374], [1509944293013079861, "time_macros", false, 13132628586410698089], [4684437522915235464, "libc", false, 658721871887825213], [4880290578780516359, "num_threads", false, 5909770111366739064], [5901133744777009488, "powerfmt", false, 4925779813488647710], [7695812897323945497, "itoa", false, 6848523709248540354], [9886904983647127192, "time_core", false, 1806475413472977276]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/time-ed7d2e721c6228db/dep-lib-time", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 14419220508014593114, "compile_kind": 6373778340919622063}