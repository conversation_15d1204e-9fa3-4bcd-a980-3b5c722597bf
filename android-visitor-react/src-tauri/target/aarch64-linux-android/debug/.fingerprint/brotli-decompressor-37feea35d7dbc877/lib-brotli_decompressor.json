{"rustc": 15497389221046826682, "features": "[\"alloc-stdlib\", \"std\"]", "declared_features": "[\"alloc-stdlib\", \"benchmark\", \"default\", \"disable-timer\", \"ffi-api\", \"pass-through-ffi-panics\", \"seccomp\", \"std\", \"unsafe\"]", "target": 11312988117123312042, "profile": 15657897354478470176, "path": 15895099561357177502, "deps": [[9611597350722197978, "alloc_no_stdlib", false, 8730054341847001290], [17470296833448545982, "alloc_stdlib", false, 7969000347283793448]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/brotli-decompressor-37feea35d7dbc877/dep-lib-brotli_decompressor", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 14419220508014593114, "compile_kind": 6373778340919622063}