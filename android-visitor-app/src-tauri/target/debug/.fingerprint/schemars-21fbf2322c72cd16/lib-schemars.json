{"rustc": 15497389221046826682, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 5347358027863023418, "path": 5722501408507984729, "deps": [[3150220818285335163, "url", false, 17957392633515370489], [6913375703034175521, "build_script_build", false, 15238307249585533332], [8319709847752024821, "uuid1", false, 16496221385851979406], [9122563107207267705, "dyn_clone", false, 16562952849623332665], [9689903380558560274, "serde", false, 12606620631701233233], [14923790796823607459, "indexmap", false, 14974066763972840966], [15367738274754116744, "serde_json", false, 13294062826417579792], [16071897500792579091, "schemars_derive", false, 192220401828493757]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/schemars-21fbf2322c72cd16/dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}