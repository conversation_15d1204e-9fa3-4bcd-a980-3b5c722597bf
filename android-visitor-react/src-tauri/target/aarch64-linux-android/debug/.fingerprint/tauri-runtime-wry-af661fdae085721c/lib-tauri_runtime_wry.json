{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 17714499385232339250, "deps": [[442785307232013896, "tauri_runtime", false, 7814358652572678663], [3150220818285335163, "url", false, 11491089270991203171], [4143744114649553716, "raw_window_handle", false, 5648965349049443791], [5986029879202738730, "log", false, 7193723165178592537], [7752760652095876438, "build_script_build", false, 17556447087959216153], [9010263965687315507, "http", false, 2369103616261986952], [11050281405049894993, "tauri_utils", false, 1870173825080044793], [13223659721939363523, "tao", false, 1230140028506467026], [13385779688343444241, "jni", false, 4007192220370321821], [14794439852947137341, "wry", false, 10403352362803383286]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/tauri-runtime-wry-af661fdae085721c/dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 14419220508014593114, "compile_kind": 6373778340919622063}