{"rustc": 15497389221046826682, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 5347358027863023418, "path": 1113195792131403990, "deps": [[561782849581144631, "html5ever", false, 9029280917050811187], [1200537532907108615, "url<PERSON><PERSON>n", false, 14507587493541009827], [3060637413840920116, "proc_macro2", false, 1047139958375200652], [3150220818285335163, "url", false, 17957392633515370489], [3191507132440681679, "serde_untagged", false, 5303995795860625482], [4899080583175475170, "semver", false, 10975467168664763669], [5578504951057029730, "serde_with", false, 8434065455157582781], [5986029879202738730, "log", false, 91875366740807208], [6262254372177975231, "kuchiki", false, 11417065414257761910], [6606131838865521726, "ctor", false, 9488040105239268467], [6913375703034175521, "schemars", false, 4956578447511972334], [7170110829644101142, "json_patch", false, 8210624417054658907], [8319709847752024821, "uuid", false, 16496221385851979406], [9010263965687315507, "http", false, 5623588973924976459], [9451456094439810778, "regex", false, 7648453349945761482], [9689903380558560274, "serde", false, 12606620631701233233], [10806645703491011684, "thiserror", false, 1165362114995630591], [11655476559277113544, "cargo_metadata", false, 8311918495964163439], [11989259058781683633, "dunce", false, 11321910025668735565], [13625485746686963219, "anyhow", false, 2772218789918005732], [14132538657330703225, "brotli", false, 18284662019197505882], [14885200901422974105, "swift_rs", false, 17909209961004914058], [15367738274754116744, "serde_json", false, 13294062826417579792], [15609422047640926750, "toml", false, 277045589566222485], [15622660310229662834, "walkdir", false, 9905997250898127666], [15932120279885307830, "memchr", false, 4134062344679000583], [17146114186171651583, "infer", false, 18275598117718919580], [17155886227862585100, "glob", false, 4591972509933625239], [17186037756130803222, "phf", false, 3370886575519677485], [17990358020177143287, "quote", false, 968387064281170568]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-utils-91743730946568e7/dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}