{"rustc": 15497389221046826682, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 5347358027863023418, "path": 15681602057810985510, "deps": [[4899080583175475170, "semver", false, 10975467168664763669], [6913375703034175521, "schemars", false, 4956578447511972334], [7170110829644101142, "json_patch", false, 8210624417054658907], [9689903380558560274, "serde", false, 12606620631701233233], [11050281405049894993, "tauri_utils", false, 13176232029506110674], [12714016054753183456, "tauri_winres", false, 16254146768043518390], [13077543566650298139, "heck", false, 9338748295320284001], [13475171727366188400, "cargo_toml", false, 3355174187775220842], [13625485746686963219, "anyhow", false, 2772218789918005732], [15367738274754116744, "serde_json", false, 13294062826417579792], [15609422047640926750, "toml", false, 277045589566222485], [15622660310229662834, "walkdir", false, 9905997250898127666], [16928111194414003569, "dirs", false, 12112916714889422385], [17155886227862585100, "glob", false, 4591972509933625239]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-build-a4a57da7b7435a1c/dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}