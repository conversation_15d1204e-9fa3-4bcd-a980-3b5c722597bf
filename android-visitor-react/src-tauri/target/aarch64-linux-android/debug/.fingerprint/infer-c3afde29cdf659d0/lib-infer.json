{"rustc": 15497389221046826682, "features": "[\"alloc\", \"cfb\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"cfb\", \"default\", \"std\"]", "target": 17568545270158767465, "profile": 15657897354478470176, "path": 9643684165281246241, "deps": [[3381430663848060157, "cfb", false, 4709537503485595573]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/infer-c3afde29cdf659d0/dep-lib-infer", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 14419220508014593114, "compile_kind": 6373778340919622063}