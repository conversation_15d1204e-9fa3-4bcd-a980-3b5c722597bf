cargo:rerun-if-env-changed=WRY_ANDROID_PACKAGE
cargo:rerun-if-env-changed=WRY_ANDROID_LIBRARY
cargo:rerun-if-env-changed=WRY_ANDROID_KOTLIN_FILES_OUT_DIR
cargo:rerun-if-changed=/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wry-0.51.2/src/android/kotlin
cargo:rerun-if-env-changed=WRY_RUSTWEBVIEWCLIENT_CLASS_EXTENSION
cargo:rerun-if-env-changed=WRY_RUSTWEBVIEWCLIENT_CLASS_INIT
cargo:rerun-if-changed=/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/gen/android/app/src/main/java/com/example/androidvisitorreact/generated/RustWebViewClient.kt
cargo:rerun-if-env-changed=WRY_PERMISSIONHELPER_CLASS_EXTENSION
cargo:rerun-if-env-changed=WRY_PERMISSIONHELPER_CLASS_INIT
cargo:rerun-if-changed=/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/gen/android/app/src/main/java/com/example/androidvisitorreact/generated/PermissionHelper.kt
cargo:rerun-if-env-changed=WRY_WRYACTIVITY_CLASS_EXTENSION
cargo:rerun-if-env-changed=WRY_WRYACTIVITY_CLASS_INIT
cargo:rerun-if-changed=/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/gen/android/app/src/main/java/com/example/androidvisitorreact/generated/WryActivity.kt
cargo:rerun-if-env-changed=WRY_RUSTWEBVIEW_CLASS_EXTENSION
cargo:rerun-if-env-changed=WRY_RUSTWEBVIEW_CLASS_INIT
cargo:rerun-if-changed=/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/gen/android/app/src/main/java/com/example/androidvisitorreact/generated/RustWebView.kt
cargo:rerun-if-env-changed=WRY_PROGUARD-WRY_CLASS_EXTENSION
cargo:rerun-if-env-changed=WRY_PROGUARD-WRY_CLASS_INIT
cargo:rerun-if-changed=/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/gen/android/app/src/main/java/com/example/androidvisitorreact/generated/proguard-wry.pro
cargo:rerun-if-env-changed=WRY_RUSTWEBCHROMECLIENT_CLASS_EXTENSION
cargo:rerun-if-env-changed=WRY_RUSTWEBCHROMECLIENT_CLASS_INIT
cargo:rerun-if-changed=/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/gen/android/app/src/main/java/com/example/androidvisitorreact/generated/RustWebChromeClient.kt
cargo:rerun-if-env-changed=WRY_LOGGER_CLASS_EXTENSION
cargo:rerun-if-env-changed=WRY_LOGGER_CLASS_INIT
cargo:rerun-if-changed=/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/gen/android/app/src/main/java/com/example/androidvisitorreact/generated/Logger.kt
cargo:rerun-if-env-changed=WRY_IPC_CLASS_EXTENSION
cargo:rerun-if-env-changed=WRY_IPC_CLASS_INIT
cargo:rerun-if-changed=/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/gen/android/app/src/main/java/com/example/androidvisitorreact/generated/Ipc.kt
