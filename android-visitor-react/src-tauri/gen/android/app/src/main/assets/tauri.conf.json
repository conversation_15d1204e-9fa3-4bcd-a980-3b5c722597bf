{"$schema": "https://schema.tauri.app/config/2.0.0-rc", "productName": "android-visitor-react", "version": "0.1.0", "identifier": "com.example.androidvisitorreact", "app": {"windows": [{"label": "main", "create": true, "url": "index.html", "dragDropEnabled": true, "center": false, "width": 800.0, "height": 600.0, "resizable": true, "maximizable": true, "minimizable": true, "closable": true, "title": "android-visitor-react", "fullscreen": false, "focus": true, "transparent": false, "maximized": false, "visible": true, "decorations": true, "alwaysOnBottom": false, "alwaysOnTop": false, "visibleOnAllWorkspaces": false, "contentProtected": false, "skipTaskbar": false, "titleBarStyle": "Visible", "hiddenTitle": false, "acceptFirstMouse": false, "shadow": true, "incognito": false, "zoomHotkeysEnabled": false, "browserExtensionsEnabled": false}], "security": {"freezePrototype": false, "dangerousDisableAssetCspModification": false, "assetProtocol": {"scope": [], "enable": false}, "pattern": {"use": "brownfield"}, "capabilities": []}, "macOSPrivateApi": false, "withGlobalTauri": false, "enableGTKAppId": false}, "build": {"devUrl": "http://localhost:5173/", "frontendDist": "../dist", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "bundle": {"active": true, "targets": "all", "createUpdaterArtifacts": false, "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "useLocalToolsDir": false, "windows": {"digestAlgorithm": null, "certificateThumbprint": null, "timestampUrl": null, "tsp": false, "webviewInstallMode": {"type": "downloadBootstrapper", "silent": true}, "allowDowngrades": true, "wix": null, "nsis": null, "signCommand": null}, "linux": {"appimage": {"bundleMediaFramework": false, "files": {}}, "deb": {"files": {}}, "rpm": {"release": "1", "epoch": 0, "files": {}}}, "macOS": {"files": {}, "minimumSystemVersion": "10.13", "hardenedRuntime": true, "dmg": {"windowSize": {"width": 660, "height": 400}, "appPosition": {"x": 180, "y": 170}, "applicationFolderPosition": {"x": 480, "y": 170}}}, "iOS": {"minimumSystemVersion": "13.0"}, "android": {"minSdkVersion": 24}}, "plugins": {}}