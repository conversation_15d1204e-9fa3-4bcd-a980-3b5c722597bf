{"rustc": 15497389221046826682, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 10217394597939959909, "deps": [[1615478164327904835, "pin_utils", false, 8835904160867229817], [1906322745568073236, "pin_project_lite", false, 6855498210364891705], [5451793922601807560, "slab", false, 4635559794588445687], [7620660491849607393, "futures_core", false, 11408040651340720204], [10565019901765856648, "futures_macro", false, 1484563042218147440], [16240732885093539806, "futures_task", false, 10822638099597893485]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/futures-util-4b9cdf62148d6fb4/dep-lib-futures_util", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 14419220508014593114, "compile_kind": 6373778340919622063}