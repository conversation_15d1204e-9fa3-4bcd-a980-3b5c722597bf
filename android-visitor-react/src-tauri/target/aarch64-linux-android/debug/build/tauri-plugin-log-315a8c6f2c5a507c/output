cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/target/aarch64-linux-android/debug/build/tauri-plugin-log-315a8c6f2c5a507c/out/tauri-plugin-log-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-log-2.5.0/api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-cfg=mobile
cargo:rustc-check-cfg=cfg(desktop)
