{"rustc": 15497389221046826682, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 5347358027863023418, "path": 4843958571946627215, "deps": [[3060637413840920116, "proc_macro2", false, 1047139958375200652], [4974441333307933176, "syn", false, 1634282311462682312], [7341521034400937459, "tauri_codegen", false, 18226123409329595000], [11050281405049894993, "tauri_utils", false, 13176232029506110674], [13077543566650298139, "heck", false, 9338748295320284001], [17990358020177143287, "quote", false, 968387064281170568]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-macros-664bda54726206e4/dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}