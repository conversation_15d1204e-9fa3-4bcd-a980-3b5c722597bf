{"rustc": 15497389221046826682, "features": "[\"__common\", \"futures-core\", \"futures-util\", \"pin-project-lite\", \"sync_wrapper\", \"timeout\", \"tokio\", \"util\"]", "declared_features": "[\"__common\", \"balance\", \"buffer\", \"discover\", \"filter\", \"full\", \"futures-core\", \"futures-util\", \"hdrhistogram\", \"hedge\", \"indexmap\", \"limit\", \"load\", \"load-shed\", \"log\", \"make\", \"pin-project-lite\", \"ready-cache\", \"reconnect\", \"retry\", \"slab\", \"spawn-ready\", \"steer\", \"sync_wrapper\", \"timeout\", \"tokio\", \"tokio-stream\", \"tokio-util\", \"tracing\", \"util\"]", "target": 12249542225364378818, "profile": 15657897354478470176, "path": 3440418223362125284, "deps": [[784494742817713399, "tower_service", false, 17120477135135066597], [1906322745568073236, "pin_project_lite", false, 6855498210364891705], [2517136641825875337, "sync_wrapper", false, 10669734765578047955], [7620660491849607393, "futures_core", false, 11408040651340720204], [7712452662827335977, "tower_layer", false, 17152465969768704217], [9538054652646069845, "tokio", false, 4926439183466968676], [10629569228670356391, "futures_util", false, 4244138559707762547]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/tower-a1060a34f1e788c3/dep-lib-tower", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 14419220508014593114, "compile_kind": 6373778340919622063}