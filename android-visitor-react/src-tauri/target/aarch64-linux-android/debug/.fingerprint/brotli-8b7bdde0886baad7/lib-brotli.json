{"rustc": 15497389221046826682, "features": "[\"alloc-stdlib\", \"std\"]", "declared_features": "[\"alloc-stdlib\", \"benchmark\", \"billing\", \"default\", \"disable-timer\", \"disallow_large_window_size\", \"external-literal-probability\", \"ffi-api\", \"float64\", \"floating_point_context_mixing\", \"no-stdlib-ffi-binding\", \"pass-through-ffi-panics\", \"seccomp\", \"sha2\", \"simd\", \"std\", \"validation\", \"vector_scratch_space\"]", "target": 7073890835992331790, "profile": 15657897354478470176, "path": 17198752504666928923, "deps": [[2568673438000342723, "brotli_decompressor", false, 15975700311355075454], [9611597350722197978, "alloc_no_stdlib", false, 8730054341847001290], [17470296833448545982, "alloc_stdlib", false, 7969000347283793448]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/brotli-8b7bdde0886baad7/dep-lib-brotli", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 14419220508014593114, "compile_kind": 6373778340919622063}