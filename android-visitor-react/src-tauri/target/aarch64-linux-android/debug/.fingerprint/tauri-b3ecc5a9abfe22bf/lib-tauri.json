{"rustc": 15497389221046826682, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 16665703716223131817, "deps": [[40386456601120721, "percent_encoding", false, 6799656999857508170], [442785307232013896, "tauri_runtime", false, 7814358652572678663], [1200537532907108615, "url<PERSON><PERSON>n", false, 6319366779144689428], [3150220818285335163, "url", false, 11491089270991203171], [4143744114649553716, "raw_window_handle", false, 5648965349049443791], [4919829919303820331, "serialize_to_javascript", false, 16506004592320139167], [5986029879202738730, "log", false, 7193723165178592537], [7752760652095876438, "tauri_runtime_wry", false, 6190537631585632445], [8218178811151724123, "reqwest", false, 14704293940156698470], [9010263965687315507, "http", false, 2369103616261986952], [9228235415475680086, "tauri_macros", false, 645372349919707646], [9538054652646069845, "tokio", false, 4926439183466968676], [9689903380558560274, "serde", false, 4550200480456870996], [9920160576179037441, "getrandom", false, 4611012767238582268], [10229185211513642314, "mime", false, 16613442265977883196], [10629569228670356391, "futures_util", false, 4244138559707762547], [10755362358622467486, "build_script_build", false, 15807458171193881623], [10806645703491011684, "thiserror", false, 2307957541569721560], [11050281405049894993, "tauri_utils", false, 1870173825080044793], [11989259058781683633, "dunce", false, 11459832735058152699], [12986574360607194341, "serde_repr", false, 14645873340333857561], [13077543566650298139, "heck", false, 5754754722490484826], [13385779688343444241, "jni", false, 4007192220370321821], [13625485746686963219, "anyhow", false, 1947825649377229231], [15367738274754116744, "serde_json", false, 11873073849529480830], [16066129441945555748, "bytes", false, 17260108681923346918], [16928111194414003569, "dirs", false, 14214591047341320488], [17155886227862585100, "glob", false, 3396416305925441589]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/tauri-b3ecc5a9abfe22bf/dep-lib-tauri", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 14419220508014593114, "compile_kind": 6373778340919622063}