{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 16244099637825074703, "profile": 15657897354478470176, "path": 10705236680223471710, "deps": [[2828590642173593838, "cfg_if", false, 714262850666453779], [4684437522915235464, "libc", false, 658721871887825213]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/getrandom-ddf8ed5d4391f829/dep-lib-getrandom", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 14419220508014593114, "compile_kind": 6373778340919622063}