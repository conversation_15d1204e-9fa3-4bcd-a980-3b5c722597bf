{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14794439852947137341, "build_script_build", false, 1345499505694354125]], "local": [{"RerunIfChanged": {"output": "aarch64-linux-android/debug/build/wry-58a9c16886e015c2/output", "paths": ["src/android/kotlin", "/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/gen/android/app/src/main/java/com/example/androidvisitorreact/generated/RustWebViewClient.kt", "/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/gen/android/app/src/main/java/com/example/androidvisitorreact/generated/PermissionHelper.kt", "/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/gen/android/app/src/main/java/com/example/androidvisitorreact/generated/WryActivity.kt", "/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/gen/android/app/src/main/java/com/example/androidvisitorreact/generated/RustWebView.kt", "/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/gen/android/app/src/main/java/com/example/androidvisitorreact/generated/proguard-wry.pro", "/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/gen/android/app/src/main/java/com/example/androidvisitorreact/generated/RustWebChromeClient.kt", "/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/gen/android/app/src/main/java/com/example/androidvisitorreact/generated/Logger.kt", "/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/gen/android/app/src/main/java/com/example/androidvisitorreact/generated/Ipc.kt"]}}, {"RerunIfEnvChanged": {"var": "WRY_ANDROID_PACKAGE", "val": "com.example.androidvisitorreact"}}, {"RerunIfEnvChanged": {"var": "WRY_ANDROID_LIBRARY", "val": "app_lib"}}, {"RerunIfEnvChanged": {"var": "WRY_ANDROID_KOTLIN_FILES_OUT_DIR", "val": "/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/gen/android/app/src/main/java/com/example/androidvisitorreact/generated"}}, {"RerunIfEnvChanged": {"var": "WRY_RUSTWEBVIEWCLIENT_CLASS_EXTENSION", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_RUSTWEBVIEWCLIENT_CLASS_INIT", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_PERMISSIONH<PERSON>PER_CLASS_EXTENSION", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_PERMISSIONHELPER_CLASS_INIT", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_WRYACTIVITY_CLASS_EXTENSION", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_WRYACTIVITY_CLASS_INIT", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_RUSTWEBVIEW_CLASS_EXTENSION", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_RUSTWEBVIEW_CLASS_INIT", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_PROGUARD-WRY_CLASS_EXTENSION", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_PROGUARD-WRY_CLASS_INIT", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_RUSTWEBCHROMECLIENT_CLASS_EXTENSION", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_RUSTWEBCHROMECLIENT_CLASS_INIT", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_LOGGER_CLASS_EXTENSION", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_LOGGER_CLASS_INIT", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_IPC_CLASS_EXTENSION", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_IPC_CLASS_INIT", "val": null}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 0, "compile_kind": 0}