{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 13146082306685502274], [10755362358622467486, "build_script_build", false, 15807458171193881623], [6343145987997818134, "build_script_build", false, 13624350370533945588]], "local": [{"RerunIfChanged": {"output": "aarch64-linux-android/debug/build/app-62e6ea864e14a076/output", "paths": ["tauri.conf.json", "gen/android/tauri.settings.gradle", "gen/android/app/tauri.build.gradle.kts", "gen/android/app/tauri.properties", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 0, "compile_kind": 0}