{"rustc": 15497389221046826682, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 15657897354478470176, "path": 13354481494640435873, "deps": [[784494742817713399, "tower_service", false, 17120477135135066597], [1906322745568073236, "pin_project_lite", false, 6855498210364891705], [4121350475192885151, "iri_string", false, 16769915519877208915], [5695049318159433696, "tower", false, 9727160625298968294], [7712452662827335977, "tower_layer", false, 17152465969768704217], [7896293946984509699, "bitflags", false, 15585780327617686466], [9010263965687315507, "http", false, 2369103616261986952], [10629569228670356391, "futures_util", false, 4244138559707762547], [14084095096285906100, "http_body", false, 12046939792429373851], [16066129441945555748, "bytes", false, 17260108681923346918]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/tower-http-bfbe709261e29beb/dep-lib-tower_http", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 14419220508014593114, "compile_kind": 6373778340919622063}