# THIS FILE IS AUTO-GENERATED. DO NOT MODIFY!!

# Copyright 2020-2023 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT

-keep class com.example.androidvisitorreact.* {
  native <methods>;
}

-keep class com.example.androidvisitorreact.WryActivity {
  public <init>(...);

  void setWebView(com.example.androidvisitorreact.RustWebView);
  java.lang.Class getAppClass(...);
  java.lang.String getVersion();
}

-keep class com.example.androidvisitorreact.Ipc {
  public <init>(...);

  @android.webkit.JavascriptInterface public <methods>;
}

-keep class com.example.androidvisitorreact.RustWebView {
  public <init>(...);

  void loadUrlMainThread(...);
  void loadHTMLMainThread(...);
  void evalScript(...);
}

-keep class com.example.androidvisitorreact.RustWebChromeClient,com.example.androidvisitorreact.RustWebViewClient {
  public <init>(...);
}
