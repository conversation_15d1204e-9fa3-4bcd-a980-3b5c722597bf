{"rustc": 15497389221046826682, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 5347358027863023418, "path": 18364188027914841832, "deps": [[2616743947975331138, "plist", false, 6287427296487665952], [3060637413840920116, "proc_macro2", false, 1047139958375200652], [3150220818285335163, "url", false, 17957392633515370489], [4899080583175475170, "semver", false, 10975467168664763669], [4974441333307933176, "syn", false, 1634282311462682312], [7170110829644101142, "json_patch", false, 8210624417054658907], [7392050791754369441, "ico", false, 13095148292464971843], [8319709847752024821, "uuid", false, 16496221385851979406], [9689903380558560274, "serde", false, 12606620631701233233], [9857275760291862238, "sha2", false, 1066178520613379097], [10806645703491011684, "thiserror", false, 1165362114995630591], [11050281405049894993, "tauri_utils", false, 13176232029506110674], [12409575957772518135, "time", false, 15515602174352822702], [12687914511023397207, "png", false, 12134803574958025887], [13077212702700853852, "base64", false, 5347602821856189146], [14132538657330703225, "brotli", false, 18284662019197505882], [15367738274754116744, "serde_json", false, 13294062826417579792], [15622660310229662834, "walkdir", false, 9905997250898127666], [17990358020177143287, "quote", false, 968387064281170568]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-codegen-b921172f6c2223ff/dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}