cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-cfg=mobile
cargo:rerun-if-changed=/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/tauri.conf.json
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_APP_NAME=androidvisitorreact
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_PREFIX=com_example
cargo:rerun-if-changed=/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/gen/android/tauri.settings.gradle
cargo:rerun-if-changed=/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/gen/android/app/tauri.build.gradle.kts
cargo:rerun-if-changed=/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/gen/android/app/tauri.properties
cargo:rustc-check-cfg=cfg(dev)
cargo:rustc-cfg=dev
cargo:PERMISSION_FILES_PATH=/Users/<USER>/my_project/项目练习-/tauri-android/android-visitor-react/src-tauri/target/aarch64-linux-android/debug/build/app-62e6ea864e14a076/out/app-manifest/__app__-permission-files
cargo:rerun-if-changed=capabilities
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:rustc-env=TAURI_ENV_TARGET_TRIPLE=aarch64-linux-android
