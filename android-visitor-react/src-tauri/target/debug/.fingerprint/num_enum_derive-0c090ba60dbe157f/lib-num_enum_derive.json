{"rustc": 15497389221046826682, "features": "[\"proc-macro-crate\", \"std\"]", "declared_features": "[\"complex-expressions\", \"default\", \"external_doc\", \"proc-macro-crate\", \"std\"]", "target": 15019087522015688764, "profile": 5347358027863023418, "path": 10878298663370382143, "deps": [[3060637413840920116, "proc_macro2", false, 1047139958375200652], [4974441333307933176, "syn", false, 1634282311462682312], [15203748914246919255, "proc_macro_crate", false, 1462031077123431632], [17990358020177143287, "quote", false, 968387064281170568]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/num_enum_derive-0c090ba60dbe157f/dep-lib-num_enum_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}