{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 5696904507869020657, "deps": [[442785307232013896, "build_script_build", false, 7653153907385331993], [3150220818285335163, "url", false, 11491089270991203171], [4143744114649553716, "raw_window_handle", false, 5648965349049443791], [7606335748176206944, "dpi", false, 14527913849232739585], [9010263965687315507, "http", false, 2369103616261986952], [9689903380558560274, "serde", false, 4550200480456870996], [10806645703491011684, "thiserror", false, 2307957541569721560], [11050281405049894993, "tauri_utils", false, 1870173825080044793], [13385779688343444241, "jni", false, 4007192220370321821], [15367738274754116744, "serde_json", false, 11873073849529480830], [16727543399706004146, "cookie", false, 15001064589009534618]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/tauri-runtime-0891e369cd752420/dep-lib-tauri_runtime", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 14419220508014593114, "compile_kind": 6373778340919622063}